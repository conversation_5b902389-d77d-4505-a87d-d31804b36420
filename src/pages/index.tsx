import RootLayout from '@/layouts/RootLayout'
import { tabSelect, tabUnSelect } from '@/components/primitives.ts'
import { Button, Image, useDisclosure } from '@heroui/react'
import Bridge from '@/components/Bridge'
import { SettingDialog } from '@/components/SettingDialog'
import { HistoryDialog } from '@/components/HistoryDialog'

export default function IndexPage() {
  const { isOpen: isSettingOpen, onOpen: onSettingOpen, onOpenChange: onSettingOpenChange } = useDisclosure()
  const { isOpen: isHistoryOpen, onOpen: onHistoryOpen, onOpenChange: onHistoryOpenChange } = useDisclosure()
  return (
    <RootLayout>
      <section className="flex flex-col items-center justify-center pt-5">
        <div className="flex items-center justify-between w-full pb-5">
          <div>
            <Button className={tabSelect()} radius="full">
              Bridge
            </Button>
            <Button className={tabUnSelect()} radius="full">
              Buy
            </Button>
          </div>
          <div className="flex items-center justify-between">
            <Button
              isIconOnly
              className="min-w-6 h-6 p-0"
              variant="light"
              onPress={onHistoryOpen}
            >
              <Image className="size-6" src="/public/images/icon_history.svg" />
            </Button>
            <Button isIconOnly className="min-w-6 h-6 p-0 ml-1" variant="light" onPress={onSettingOpen}>
              <Image className="size-6" src="/public/images/icon_setting.svg" />
            </Button>
          </div>
        </div>
        <Bridge />

        <SettingDialog isOpen={isSettingOpen} onOpenChange={onSettingOpenChange} />
        <HistoryDialog isOpen={isHistoryOpen} onOpenChange={onHistoryOpenChange} />
      </section>
    </RootLayout>
  )
}
