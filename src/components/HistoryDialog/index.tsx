import React, { useState, useMemo } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>ontent, <PERSON>dal<PERSON>eader, ModalBody, Button, Image, Pagination } from '@heroui/react'

export interface HistoryDialogProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
}

// Mock transaction data - 25 transactions for pagination testing
const mockTransactions = [
  {
    id: 1,
    hash: '0xc49...ef4d',
    asset: { symbol: 'XNET', icon: '/public/images/tokens/xnet.svg' },
    from: { name: 'Polygon', icon: '/public/images/networks/polygon.svg' },
    to: { name: 'IoTeX', icon: '/public/images/networks/iotex.svg' },
    status: 'Resolved',
    age: '2 h ago',
  },
  {
    id: 2,
    hash: '0xa1b...2c3d',
    asset: { symbol: 'USDT', icon: '/public/images/tokens/usdt.svg' },
    from: { name: 'Ethereum', icon: '/public/images/networks/ethereum.svg' },
    to: { name: 'BSC', icon: '/public/images/networks/bsc.svg' },
    status: 'Resolved',
    age: '3 h ago',
  },
  {
    id: 3,
    hash: '0xf5e...8a9b',
    asset: { symbol: 'IOTX', icon: '/public/images/tokens/iotx.svg' },
    from: { name: 'IoTeX', icon: '/public/images/networks/iotex.svg' },
    to: { name: 'Ethereum', icon: '/public/images/networks/ethereum.svg' },
    status: 'Confirming on IOTEX',
    age: '5 h ago',
  },
  {
    id: 4,
    hash: '0x123...456e',
    asset: { symbol: 'XNET', icon: '/public/images/tokens/xnet.svg' },
    from: { name: 'BSC', icon: '/public/images/networks/bsc.svg' },
    to: { name: 'Polygon', icon: '/public/images/networks/polygon.svg' },
    status: 'Resolved',
    age: '1 day ago',
  },
  {
    id: 5,
    hash: '0x789...abc0',
    asset: { symbol: 'USDC', icon: '/public/images/tokens/usdc.svg' },
    from: { name: 'Ethereum', icon: '/public/images/networks/ethereum.svg' },
    to: { name: 'Polygon', icon: '/public/images/networks/polygon.svg' },
    status: 'Resolved',
    age: '1 day ago',
  },
  {
    id: 6,
    hash: '0xdef...123g',
    asset: { symbol: 'XNET', icon: '/public/images/tokens/xnet.svg' },
    from: { name: 'IoTeX', icon: '/public/images/networks/iotex.svg' },
    to: { name: 'BSC', icon: '/public/images/networks/bsc.svg' },
    status: 'Confirming on BSC',
    age: '2 days ago',
  },
  {
    id: 7,
    hash: '0x456...789h',
    asset: { symbol: 'BNB', icon: '/public/images/tokens/bnb.svg' },
    from: { name: 'BSC', icon: '/public/images/networks/bsc.svg' },
    to: { name: 'Ethereum', icon: '/public/images/networks/ethereum.svg' },
    status: 'Resolved',
    age: '3 days ago',
  },
  {
    id: 8,
    hash: '0x987...654i',
    asset: { symbol: 'IOTX', icon: '/public/images/tokens/iotx.svg' },
    from: { name: 'Polygon', icon: '/public/images/networks/polygon.svg' },
    to: { name: 'IoTeX', icon: '/public/images/networks/iotex.svg' },
    status: 'Resolved',
    age: '4 days ago',
  },
  {
    id: 9,
    hash: '0xabc...def1',
    asset: { symbol: 'ETH', icon: '/public/images/tokens/eth.svg' },
    from: { name: 'Ethereum', icon: '/public/images/networks/ethereum.svg' },
    to: { name: 'IoTeX', icon: '/public/images/networks/iotex.svg' },
    status: 'Resolved',
    age: '5 days ago',
  },
  {
    id: 10,
    hash: '0x321...cba2',
    asset: { symbol: 'USDT', icon: '/public/images/tokens/usdt.svg' },
    from: { name: 'BSC', icon: '/public/images/networks/bsc.svg' },
    to: { name: 'Polygon', icon: '/public/images/networks/polygon.svg' },
    status: 'Resolved',
    age: '6 days ago',
  },
  // Additional transactions for pagination testing
  {
    id: 11,
    hash: '0x111...222j',
    asset: { symbol: 'XNET', icon: '/public/images/tokens/xnet.svg' },
    from: { name: 'Polygon', icon: '/public/images/networks/polygon.svg' },
    to: { name: 'IoTeX', icon: '/public/images/networks/iotex.svg' },
    status: 'Confirming on IOTEX',
    age: '1 week ago',
  },
  {
    id: 12,
    hash: '0x333...444k',
    asset: { symbol: 'IOTX', icon: '/public/images/tokens/iotx.svg' },
    from: { name: 'IoTeX', icon: '/public/images/networks/iotex.svg' },
    to: { name: 'Ethereum', icon: '/public/images/networks/ethereum.svg' },
    status: 'Resolved',
    age: '1 week ago',
  },
  {
    id: 13,
    hash: '0x555...666l',
    asset: { symbol: 'USDC', icon: '/public/images/tokens/usdc.svg' },
    from: { name: 'Ethereum', icon: '/public/images/networks/ethereum.svg' },
    to: { name: 'BSC', icon: '/public/images/networks/bsc.svg' },
    status: 'Resolved',
    age: '1 week ago',
  },
  {
    id: 14,
    hash: '0x777...888m',
    asset: { symbol: 'BNB', icon: '/public/images/tokens/bnb.svg' },
    from: { name: 'BSC', icon: '/public/images/networks/bsc.svg' },
    to: { name: 'Polygon', icon: '/public/images/networks/polygon.svg' },
    status: 'Resolved',
    age: '2 weeks ago',
  },
  {
    id: 15,
    hash: '0x999...000n',
    asset: { symbol: 'XNET', icon: '/public/images/tokens/xnet.svg' },
    from: { name: 'Polygon', icon: '/public/images/networks/polygon.svg' },
    to: { name: 'IoTeX', icon: '/public/images/networks/iotex.svg' },
    status: 'Resolved',
    age: '2 weeks ago',
  },
  {
    id: 16,
    hash: '0xaaa...bbbo',
    asset: { symbol: 'ETH', icon: '/public/images/tokens/eth.svg' },
    from: { name: 'Ethereum', icon: '/public/images/networks/ethereum.svg' },
    to: { name: 'Polygon', icon: '/public/images/networks/polygon.svg' },
    status: 'Resolved',
    age: '3 weeks ago',
  },
  {
    id: 17,
    hash: '0xccc...dddp',
    asset: { symbol: 'USDT', icon: '/public/images/tokens/usdt.svg' },
    from: { name: 'BSC', icon: '/public/images/networks/bsc.svg' },
    to: { name: 'IoTeX', icon: '/public/images/networks/iotex.svg' },
    status: 'Resolved',
    age: '3 weeks ago',
  },
  {
    id: 18,
    hash: '0xeee...fffq',
    asset: { symbol: 'IOTX', icon: '/public/images/tokens/iotx.svg' },
    from: { name: 'IoTeX', icon: '/public/images/networks/iotex.svg' },
    to: { name: 'BSC', icon: '/public/images/networks/bsc.svg' },
    status: 'Resolved',
    age: '1 month ago',
  },
  {
    id: 19,
    hash: '0x123...789r',
    asset: { symbol: 'USDC', icon: '/public/images/tokens/usdc.svg' },
    from: { name: 'Polygon', icon: '/public/images/networks/polygon.svg' },
    to: { name: 'Ethereum', icon: '/public/images/networks/ethereum.svg' },
    status: 'Resolved',
    age: '1 month ago',
  },
  {
    id: 20,
    hash: '0x456...012s',
    asset: { symbol: 'BNB', icon: '/public/images/tokens/bnb.svg' },
    from: { name: 'BSC', icon: '/public/images/networks/bsc.svg' },
    to: { name: 'IoTeX', icon: '/public/images/networks/iotex.svg' },
    status: 'Resolved',
    age: '1 month ago',
  },
  {
    id: 21,
    hash: '0x789...345t',
    asset: { symbol: 'XNET', icon: '/public/images/tokens/xnet.svg' },
    from: { name: 'IoTeX', icon: '/public/images/networks/iotex.svg' },
    to: { name: 'Polygon', icon: '/public/images/networks/polygon.svg' },
    status: 'Resolved',
    age: '2 months ago',
  },
  {
    id: 22,
    hash: '0xabc...678u',
    asset: { symbol: 'ETH', icon: '/public/images/tokens/eth.svg' },
    from: { name: 'Ethereum', icon: '/public/images/networks/ethereum.svg' },
    to: { name: 'BSC', icon: '/public/images/networks/bsc.svg' },
    status: 'Resolved',
    age: '2 months ago',
  },
  {
    id: 23,
    hash: '0xdef...901v',
    asset: { symbol: 'USDT', icon: '/public/images/tokens/usdt.svg' },
    from: { name: 'Polygon', icon: '/public/images/networks/polygon.svg' },
    to: { name: 'BSC', icon: '/public/images/networks/bsc.svg' },
    status: 'Resolved',
    age: '2 months ago',
  },
  {
    id: 24,
    hash: '0x234...567w',
    asset: { symbol: 'IOTX', icon: '/public/images/tokens/iotx.svg' },
    from: { name: 'BSC', icon: '/public/images/networks/bsc.svg' },
    to: { name: 'IoTeX', icon: '/public/images/networks/iotex.svg' },
    status: 'Resolved',
    age: '3 months ago',
  },
  {
    id: 25,
    hash: '0x567...890x',
    asset: { symbol: 'USDC', icon: '/public/images/tokens/usdc.svg' },
    from: { name: 'Ethereum', icon: '/public/images/networks/ethereum.svg' },
    to: { name: 'IoTeX', icon: '/public/images/networks/iotex.svg' },
    status: 'Resolved',
    age: '3 months ago',
  },
]

export function HistoryDialog({ isOpen, onOpenChange }: HistoryDialogProps) {
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 10

  // Calculate pagination
  const totalPages = Math.ceil(mockTransactions.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentTransactions = useMemo(() =>
    mockTransactions.slice(startIndex, endIndex),
    [startIndex, endIndex]
  )

  // Reset to first page when dialog opens
  React.useEffect(() => {
    if (isOpen) {
      setCurrentPage(1)
    }
  }, [isOpen])

  return (
    <Modal
      hideCloseButton
      classNames={{
        base: 'bg-color6 border border-color3 rounded-2xl max-w-[900px] w-full mx-4',
        header: 'border-b border-color3 pb-4',
        body: 'py-0',
      }}
      isOpen={isOpen}
      motionProps={{
        variants: {
          enter: {
            y: 0,
            opacity: 1,
            transition: {
              duration: 0.3,
              ease: 'easeOut',
            },
          },
          exit: {
            y: -10,
            opacity: 0,
            transition: {
              duration: 0.2,
              ease: 'easeIn',
            },
          },
        },
      }}
      size="5xl"
      onOpenChange={onOpenChange}
    >
      <ModalContent>
        {onClose => (
          <>
            <ModalHeader className="flex justify-between items-center px-6 pt-6">
              <div className="flex items-center gap-3">
                <h2 className="text-xl font-medium text-color8">Recent Transactions</h2>
                <div className="text-sm text-color7 bg-color3 px-2 py-1 rounded-full">
                  {mockTransactions.length} total
                </div>
              </div>
              <Button
                isIconOnly
                className="text-color7 hover:text-color8 hover:bg-color3 min-w-8 h-8"
                variant="light"
                onPress={onClose}
              >
                <Image className="size-5" src="/public/images/icon_close.svg" />
              </Button>
            </ModalHeader>
            <ModalBody className="px-6 pb-6">
              <div className="w-full">
                {/* Table Header */}
                <div className="grid grid-cols-6 gap-4 py-4 px-4 text-sm font-medium text-color7 bg-color3 rounded-t-lg border-b border-color9">
                  <div>Hash</div>
                  <div>Asset</div>
                  <div>From</div>
                  <div>To</div>
                  <div>Status</div>
                  <div>Age</div>
                </div>

                {/* Table Body */}
                <div className="border border-color9 border-t-0 rounded-b-lg">
                  {currentTransactions.map((transaction, index) => {
                    const isLast = index === currentTransactions.length - 1
                    return (
                      <div
                        key={transaction.id}
                        className={`grid grid-cols-6 gap-4 py-4 px-4 hover:bg-color3 transition-colors ${
                          !isLast ? 'border-b border-color9' : ''
                        }`}
                      >
                        {/* Hash */}
                        <div className="text-sm text-purple1 font-mono cursor-pointer hover:text-purple2">
                          {transaction.hash}
                        </div>

                        {/* Asset */}
                        <div className="flex items-center gap-2">
                          {getAssetIcon(transaction.asset.symbol)}
                          <span className="text-sm text-color8 font-medium">{transaction.asset.symbol}</span>
                        </div>

                        {/* From */}
                        <div className="flex items-center gap-2">
                          {getNetworkIcon(transaction.from.name)}
                          <span className="text-sm text-color8">{transaction.from.name}</span>
                        </div>

                        {/* To */}
                        <div className="flex items-center gap-2">
                          {getNetworkIcon(transaction.to.name)}
                          <span className="text-sm text-color8">{transaction.to.name}</span>
                        </div>

                        {/* Status */}
                        <div>
                          <span
                            className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusStyle(transaction.status)}`}
                          >
                            {transaction.status}
                          </span>
                        </div>

                        {/* Age */}
                        <div className="text-sm text-color7">{transaction.age}</div>
                      </div>
                    )
                  })}
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex justify-center items-center mt-6 gap-4">
                    <div className="text-sm text-color7">
                      Showing {startIndex + 1}-{Math.min(endIndex, mockTransactions.length)} of {mockTransactions.length}
                    </div>
                    <Pagination
                      classNames={{
                        wrapper: 'gap-0 overflow-visible h-8 rounded border border-color9',
                        item: 'w-8 h-8 text-small rounded-none bg-transparent text-color8',
                        cursor: 'bg-purple1 shadow-lg text-white font-bold',
                        prev: 'bg-transparent hover:bg-color3',
                        next: 'bg-transparent hover:bg-color3',
                      }}
                      page={currentPage}
                      total={totalPages}
                      variant="bordered"
                      onChange={setCurrentPage}
                    />
                  </div>
                )}
              </div>
            </ModalBody>
          </>
        )}
      </ModalContent>
    </Modal>
  )
}

// Helper functions for icons and styles
function getAssetIcon(symbol: string) {
  const iconMap: Record<string, { bg: string; text: string; letter: string }> = {
    XNET: { bg: 'bg-[#00D4FF]', text: 'text-black', letter: 'X' },
    USDT: { bg: 'bg-[#26A17B]', text: 'text-white', letter: 'U' },
    USDC: { bg: 'bg-[#2775CA]', text: 'text-white', letter: 'U' },
    IOTX: { bg: 'bg-[#00D4FF]', text: 'text-black', letter: 'I' },
    ETH: { bg: 'bg-[#627EEA]', text: 'text-white', letter: 'E' },
    BNB: { bg: 'bg-[#F3BA2F]', text: 'text-black', letter: 'B' },
  }

  const config = iconMap[symbol] || { bg: 'bg-gray-500', text: 'text-white', letter: symbol[0] }

  return (
    <div className={`w-6 h-6 rounded-full ${config.bg} flex items-center justify-center`}>
      <span className={`text-xs font-bold ${config.text}`}>{config.letter}</span>
    </div>
  )
}

function getNetworkIcon(name: string) {
  const iconMap: Record<string, { bg: string; text: string; letter: string }> = {
    Polygon: { bg: 'bg-[#8247E5]', text: 'text-white', letter: 'P' },
    IoTeX: { bg: 'bg-[#00D4FF]', text: 'text-black', letter: 'I' },
    Ethereum: { bg: 'bg-[#627EEA]', text: 'text-white', letter: 'E' },
    BSC: { bg: 'bg-[#F3BA2F]', text: 'text-black', letter: 'B' },
  }

  const config = iconMap[name] || { bg: 'bg-gray-500', text: 'text-white', letter: name[0] }

  return (
    <div className={`w-6 h-6 rounded-full ${config.bg} flex items-center justify-center`}>
      <span className={`text-xs font-bold ${config.text}`}>{config.letter}</span>
    </div>
  )
}

function getStatusStyle(status: string) {
  if (status === 'Resolved') {
    return 'bg-green-500/20 text-green-400 border border-green-500/30'
  }
  if (status.includes('Confirming')) {
    return 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30'
  }
  return 'bg-gray-500/20 text-gray-400 border border-gray-500/30'
}
