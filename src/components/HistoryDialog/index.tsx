import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, But<PERSON>, Image } from '@heroui/react'

export interface HistoryDialogProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
}

// Mock transaction data
const mockTransactions = [
  {
    id: 1,
    hash: '0xc49...ef4d',
    asset: { symbol: 'XNET', icon: '/public/images/tokens/xnet.svg' },
    from: { name: 'Polygon', icon: '/public/images/networks/polygon.svg' },
    to: { name: 'IoTeX', icon: '/public/images/networks/iotex.svg' },
    status: 'Resolved',
    age: '2 h ago',
  },
  {
    id: 2,
    hash: '0xc49...ef4d',
    asset: { symbol: 'XNET', icon: '/public/images/tokens/xnet.svg' },
    from: { name: 'Polygon', icon: '/public/images/networks/polygon.svg' },
    to: { name: 'IoTeX', icon: '/public/images/networks/iotex.svg' },
    status: 'Resolved',
    age: '2 h ago',
  },
  {
    id: 3,
    hash: '0xc49...ef4d',
    asset: { symbol: 'XNET', icon: '/public/images/tokens/xnet.svg' },
    from: { name: 'Polygon', icon: '/public/images/networks/polygon.svg' },
    to: { name: 'IoTeX', icon: '/public/images/networks/iotex.svg' },
    status: 'Resolved',
    age: '2 h ago',
  },
  {
    id: 4,
    hash: '0xc49...ef4d',
    asset: { symbol: 'XNET', icon: '/public/images/tokens/xnet.svg' },
    from: { name: 'Polygon', icon: '/public/images/networks/polygon.svg' },
    to: { name: 'IoTeX', icon: '/public/images/networks/iotex.svg' },
    status: 'Resolved',
    age: '2 h ago',
  },
  {
    id: 5,
    hash: '0xc49...ef4d',
    asset: { symbol: 'XNET', icon: '/public/images/tokens/xnet.svg' },
    from: { name: 'Polygon', icon: '/public/images/networks/polygon.svg' },
    to: { name: 'IoTeX', icon: '/public/images/networks/iotex.svg' },
    status: 'Resolved',
    age: '2 h ago',
  },
  {
    id: 6,
    hash: '0xc49...ef4d',
    asset: { symbol: 'XNET', icon: '/public/images/tokens/xnet.svg' },
    from: { name: 'Polygon', icon: '/public/images/networks/polygon.svg' },
    to: { name: 'IoTeX', icon: '/public/images/networks/iotex.svg' },
    status: 'Resolved',
    age: '2 h ago',
  },
  {
    id: 7,
    hash: '0xc49...ef4d',
    asset: { symbol: 'XNET', icon: '/public/images/tokens/xnet.svg' },
    from: { name: 'Polygon', icon: '/public/images/networks/polygon.svg' },
    to: { name: 'IoTeX', icon: '/public/images/networks/iotex.svg' },
    status: 'Resolved',
    age: '2 h ago',
  },
  {
    id: 8,
    hash: '0xc49...ef4d',
    asset: { symbol: 'XNET', icon: '/public/images/tokens/xnet.svg' },
    from: { name: 'Polygon', icon: '/public/images/networks/polygon.svg' },
    to: { name: 'IoTeX', icon: '/public/images/networks/iotex.svg' },
    status: 'Resolved',
    age: '2 h ago',
  },
  {
    id: 9,
    hash: '0xc49...ef4d',
    asset: { symbol: 'XNET', icon: '/public/images/tokens/xnet.svg' },
    from: { name: 'Polygon', icon: '/public/images/networks/polygon.svg' },
    to: { name: 'IoTeX', icon: '/public/images/networks/iotex.svg' },
    status: 'Resolved',
    age: '2 h ago',
  },
  {
    id: 10,
    hash: '0xc49...ef4d',
    asset: { symbol: 'XNET', icon: '/public/images/tokens/xnet.svg' },
    from: { name: 'Polygon', icon: '/public/images/networks/polygon.svg' },
    to: { name: 'IoTeX', icon: '/public/images/networks/iotex.svg' },
    status: 'Confirming on IOTEX',
    age: '2 h ago',
  },
]

export function HistoryDialog({ isOpen, onOpenChange }: HistoryDialogProps) {
  return (
    <Modal
      hideCloseButton
      classNames={{
        base: 'bg-[#2D2B3A] border border-[#3A3847] rounded-2xl max-w-[800px] w-full',
        header: 'border-b-0 pb-2',
        body: 'py-4',
      }}
      isOpen={isOpen}
      motionProps={{
        variants: {
          enter: {
            y: 0,
            opacity: 1,
            transition: {
              duration: 0.3,
              ease: 'easeOut',
            },
          },
          exit: {
            y: -10,
            opacity: 0,
            transition: {
              duration: 0.2,
              ease: 'easeIn',
            },
          },
        },
      }}
      size="5xl"
      onOpenChange={onOpenChange}
    >
      <ModalContent>
        {onClose => (
          <>
            <ModalHeader className="flex justify-between items-center px-6 pt-6">
              <h2 className="text-xl font-medium text-white">Recent Transactions</h2>
              <Button
                isIconOnly
                className="text-white/70 hover:text-white hover:bg-white/10 min-w-6 h-6"
                variant="light"
                onPress={onClose}
              >
                <Image className="size-6" src="/public/images/icon_close.svg" />
              </Button>
            </ModalHeader>
            <ModalBody className="px-6 pb-6">
              <div className="w-full">
                {/* Table Header */}
                <div className="grid grid-cols-6 gap-4 py-3 px-4 text-sm text-white/60 border-b border-white/10">
                  <div>Hash</div>
                  <div>Asset</div>
                  <div>From</div>
                  <div>To</div>
                  <div>Status</div>
                  <div>Age</div>
                </div>

                {/* Table Body */}
                <div className="max-h-96 overflow-y-auto custom-scrollbar">
                  {mockTransactions.map(transaction => (
                    <div
                      key={transaction.id}
                      className="grid grid-cols-6 gap-4 py-3 px-4 hover:bg-white/5 transition-colors border-b border-white/5"
                    >
                      {/* Hash */}
                      <div className="text-sm text-purple1 font-mono">{transaction.hash}</div>

                      {/* Asset */}
                      <div className="flex items-center gap-2">
                        <div className="w-6 h-6 rounded-full bg-[#00D4FF] flex items-center justify-center">
                          <span className="text-xs font-bold text-black">X</span>
                        </div>
                        <span className="text-sm text-white">{transaction.asset.symbol}</span>
                      </div>

                      {/* From */}
                      <div className="flex items-center gap-2">
                        <div className="w-6 h-6 rounded-full bg-[#8247E5] flex items-center justify-center">
                          <span className="text-xs font-bold text-white">P</span>
                        </div>
                        <span className="text-sm text-white">{transaction.from.name}</span>
                      </div>

                      {/* To */}
                      <div className="flex items-center gap-2">
                        <div className="w-6 h-6 rounded-full bg-[#00D4FF] flex items-center justify-center">
                          <span className="text-xs font-bold text-black">I</span>
                        </div>
                        <span className="text-sm text-white">{transaction.to.name}</span>
                      </div>

                      {/* Status */}
                      <div>
                        <span
                          className={`px-2 py-1 rounded-full text-xs font-medium ${
                            transaction.status === 'Resolved'
                              ? 'bg-green-500/20 text-green-400'
                              : 'bg-yellow-500/20 text-yellow-400'
                          }`}
                        >
                          {transaction.status}
                        </span>
                      </div>

                      {/* Age */}
                      <div className="text-sm text-white/60">{transaction.age}</div>
                    </div>
                  ))}
                </div>
              </div>
            </ModalBody>
          </>
        )}
      </ModalContent>
    </Modal>
  )
}
