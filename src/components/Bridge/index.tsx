import { useStore } from '@/store'
import { NetworkSelector } from '@/components/NetworkSelector'
import { networks } from '@/config/network.ts'
import { Network } from '@/types/network.ts'
import Token from '@/types/token.ts'
import { observer } from 'mobx-react-lite'
import { Button, Image, Input, useDisclosure } from '@heroui/react'
import TokenSelector from '@/components/TokenSelector'
import { isValidEvmAddress, validateAmountInput } from '@/lib/utils'
import React from 'react'
import toast from 'react-hot-toast'
import { NetworkSwitcher } from '@/components/NetworkSwitcher'
import { useAppKit } from '@reown/appkit/react'

const Bridge = observer(() => {
  const { lang, accountStore, depositStore, tokenStore } = useStore()
  const {
    isOpen: isOpenTokenSelector,
    onOpen: onOpenTokenSelector,
    onOpenChange: onOpenTokenSelectorChange,
  } = useDisclosure()
  const {
    isOpen: isOpenNetworkSwitcher,
    onOpen: onOpenNetworkSwitcher,
    onOpenChange: onOpenNetworkSwitcherChange,
  } = useDisclosure()
  const { open: openConnector } = useAppKit()

  const handleTokenSelect = React.useCallback((token: Token) => {
    depositStore.setSelectedToken(token)
  }, [])

  const handleAmountChange = React.useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value

      // 使用工具方法验证输入
      if (!validateAmountInput(value)) {
        return
      }

      depositStore.setInputAmount(value)
    },
    [depositStore]
  )

  const handleAddressChange = React.useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value

      // 设置地址值
      depositStore.setReceiptAddress(value)

      // 校验地址有效性
      if (value === '') {
        // 空地址认为是有效的（用户可能还在输入）
        depositStore.setReceiptAddressValid(true)
      } else {
        // 使用 isValidEvmAddress 校验地址
        const isValid = isValidEvmAddress(value)
        depositStore.setReceiptAddressValid(isValid)
      }
    },
    [depositStore]
  )

  // 计算按钮状态
  const isNetworkMatched = React.useMemo(() => {
    return accountStore.currentChainId === depositStore.fromNetwork.chainId
  }, [accountStore.currentChainId, depositStore.fromNetwork.chainId])

  // 分别检查数量和地址的有效性
  const isAmountValid = React.useMemo(() => {
    return depositStore.isInputAmountValid
  }, [depositStore.isInputAmountValid])

  const isAddressValid = React.useMemo(() => {
    return (
      depositStore.isReceiptAddressValid && depositStore.receiptAddress && depositStore.receiptAddress.trim() !== ''
    )
  }, [depositStore.isReceiptAddressValid, depositStore.receiptAddress])

  const isFormValid = React.useMemo(() => {
    return isAmountValid && isAddressValid
  }, [isAmountValid, isAddressValid])

  // 检查是否应该显示数量警告
  const shouldShowAmountWarning = React.useMemo(() => {
    const hasInput = depositStore.displayInputAmount && depositStore.displayInputAmount.trim() !== ''
    return hasInput && !isAmountValid
  }, [depositStore.displayInputAmount, isAmountValid])

  // 检查是否应该显示地址警告
  const shouldShowAddressWarning = React.useMemo(() => {
    const hasInput = depositStore.receiptAddress && depositStore.receiptAddress.trim() !== ''
    return hasInput && !isAddressValid
  }, [depositStore.receiptAddress, isAddressValid])

  const canClickButton = React.useMemo(() => {
    // 如果网络不匹配，可以点击（用于切换网络）
    if (!isNetworkMatched) {
      return true
    }
    // 如果网络匹配，需要表单有效才能点击
    return isFormValid
  }, [isNetworkMatched, isFormValid])

  const buttonText = React.useMemo(() => {
    if (!accountStore.isWalletConnected) {
      return 'Connect Wallet'
    }
    if (!isNetworkMatched) {
      return `Switch to ${depositStore.fromNetwork.name}`
    }
    return 'Deposit'
  }, [accountStore.isWalletConnected, isNetworkMatched, depositStore.fromNetwork.name])

  // 处理按钮点击
  const handleDepositClick = React.useCallback(async () => {
    if (!accountStore.isWalletConnected) {
      await openConnector({ view: 'Connect' })
      return
    }

    // 如果网络不匹配，切换网络
    if (!isNetworkMatched) {
      onOpenNetworkSwitcher()
      return
    }

    // 如果表单无效，不执行操作
    if (!isFormValid) {
      if (!isAmountValid) {
        toast.error('Please enter a valid amount')
      } else if (!isAddressValid) {
        toast.error('Please enter a valid receipt address')
      }
      return
    }

    // 执行存款操作
    try {
      await depositStore.depositTo()
      toast.success('Deposit successful!')
    } catch (error) {
      console.error('Deposit failed:', error)
      toast.error('Deposit failed. Please try again.')
    }
  }, [accountStore.isWalletConnected, isNetworkMatched, isFormValid, depositStore])

  return (
    <div className="w-full">
      <div className="bg-color6 border-border rounded-xl w-full px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="font-normal text-lg text-color7 w-64">From Network</div>
          <div className="w-20" />
          <div className="font-normal text-lg text-color7 w-64">Destination Network</div>
        </div>

        <div className="w-full flex items-center justify-between mt-3">
          <NetworkSelector
            className="w-64"
            networks={networks}
            selectionChainId={depositStore.fromNetwork.chainId}
            onSelectedNetwork={function (network: Network) {
              depositStore.setFromNetwork(network)
            }}
          >
            <Button className="bg-color3 rounded-full flex items-center justify-between px-3 h-12 w-64">
              <div className="flex items-center">
                <Image className="size-6" src={depositStore.fromNetwork.logoUrl} />
                <div className="text-lg text-color8 ml-2">{depositStore.fromNetwork.name}</div>
              </div>
              <Image className="size-6" src="/public/images/icon_down.svg" />
            </Button>
          </NetworkSelector>
          <Image
            className="size-12 mx-4 cursor-pointer"
            src="/public/images/icon_arrow.svg"
            onClick={() => depositStore.swapNetworks()}
          />
          <NetworkSelector
            networks={depositStore.fromNetwork.destNetworks}
            selectionChainId={depositStore.destNetwork.chainId}
            onSelectedNetwork={function (network: Network) {
              depositStore.setDestNetwork(network)
            }}
          >
            <Button className="bg-color3 rounded-full flex items-center justify-between px-3 h-12 w-64">
              <div className="flex items-center">
                <Image className="size-6 rounded-full" src={depositStore.destNetwork.logoUrl} />
                <div className="text-lg text-color8 ml-2">{depositStore.destNetwork.name}</div>
              </div>
              <Image className="size-6" src="/public/images/icon_down.svg" />
            </Button>
          </NetworkSelector>
        </div>

        <div className="flex items-center justify-between mt-6">
          <div className="text-lg text-color7">Select Token</div>
          <div className="text-sm text-color8">
            Balance: {depositStore.selectedToken?.balance?.formattedAmount} {depositStore.selectedToken?.symbol}
          </div>
        </div>

        <div className="w-full flex items-center justify-between mt-3">
          <Button
            className="bg-color3 rounded-full flex items-center justify-between px-3 h-12 w-64"
            onPress={onOpenTokenSelector}
          >
            <div className="flex items-center">
              <Image className="size-6" src={depositStore.selectedToken?.logouri} />
              <div className="text-lg text-color8 ml-2">{depositStore.selectedToken?.symbol}</div>
            </div>
            <Image className="size-6" src="/public/images/icon_down.svg" />
          </Button>
          <div className="w-6" />
          <div className="px-4 border-1 border-color9 box-border rounded-full flex items-center justify-between w-64 h-12">
            <Input
              className="mr-2 flex-1"
              classNames={{
                input: [
                  'bg-transparent',
                  'text-color8',
                  'placeholder:text-color7 dark:placeholder:text-white/60',
                  'text-lg',
                ],
                innerWrapper: 'bg-transparent',
                inputWrapper: [
                  'bg-transparent',
                  'dark:transparent',
                  'hover:bg-transparent',
                  'dark:hover:bg-transparent',
                  'group-data-[focus=true]:bg-transparent',
                  '!cursor-text',
                ],
              }}
              placeholder="0"
              type="text"
              value={depositStore.displayInputAmount}
              onChange={handleAmountChange}
            />
            <Button className="text-purple1 text-lg p-0 bg-transparent min-w-0">MAX</Button>
          </div>
        </div>
        {shouldShowAmountWarning && (
          <div className="flex justify-end">
            <div className="mt-2 w-64 text-sm text-warning-500 dark:text-warning-400">
              ✗ Please enter a valid amount
            </div>
          </div>
        )}

        <div className="mt-6 text-lg text-color7">Recipient Address</div>
        <div className="mt-3">
          <div className="border-1 border-color9 rounded-full h-12 flex items-center justify-between p-4 gap-2">
            <Input
              className="mr-2 flex-1"
              classNames={{
                input: [
                  'bg-transparent',
                  'text-color8',
                  'placeholder:text-color7 dark:placeholder:text-white/60',
                  'text-lg',
                ],
                inputWrapper: [
                  'bg-transparent',
                  'dark:transparent',
                  'hover:bg-transparent',
                  'dark:hover:bg-transparent',
                  'group-data-[focus=true]:bg-transparent',
                  '!cursor-text',
                ],
              }}
              placeholder="0x"
              value={depositStore.receiptAddress}
              onChange={handleAddressChange}
            />
            <Button
              className="text-purple1 text-lg p-0 bg-transparent"
              onPress={() => {
                if (accountStore.isWalletConnected) {
                  depositStore.setReceiptAddress(accountStore.walletAddress)
                  depositStore.setReceiptAddressValid(true)
                }
              }}
            >
              My Wallet
            </Button>
          </div>
          {shouldShowAddressWarning && (
            <div className="mt-2 text-sm text-warning-500 dark:text-warning-400">
              ✗ Please enter a valid EVM address
            </div>
          )}
        </div>

        <div className="mt-6 text-lg text-color7">Get on {depositStore.destNetwork.name}</div>
        <div className="mt-2 flex items-center">
          <Image className="size-10 rounded-full" src={depositStore.selectedToken?.logouri} />
          <div className="ml-3">
            <div className="text-color8 text-2xl">
              {depositStore.inputAmount.displayAmount || '0'} {depositStore.selectedToken?.symbol}
            </div>
            <div className="text-color7 text-xs">$2.156</div>
          </div>
        </div>

        <div className="flex items-center justify-between mt-4">
          <div className="flex items-center">
            <Image className="size-4" src="/public/images/icon_coin.svg" />
            <div className="text-xs text-color7 ml-2">Relay Fee: 0.00045ETH</div>
            <Image className="size-4 ml-4" src="/public/images/icon_exchange.svg" />
            <div className="text-xs text-color7 ml-2">
              Deposit Fee:{' '}
              {depositStore.destNetwork.crossOptions?.depositFee.value
                ? `${
                    depositStore.destNetwork.crossOptions.depositFee.formattedAmount || '0'
                  } ${depositStore.fromNetwork.nativeToken.symbol}`
                : 'Loading...'}
            </div>
          </div>
          <div className="flex items-center">
            <Image className="size-4" src="/public/images/icon_hourglass.svg" />
            <div className="text-xs text-color7 ml-2">About 5 mins</div>
          </div>
        </div>
      </div>

      <Button
        className="w-full rounded-full mt-8 h-12 font-bold text-xl bg-gradient-to-b from-purple3 to-purple4"
        disabled={!canClickButton}
        onPress={handleDepositClick}
      >
        {buttonText}
      </Button>

      <div className="text-color7 text-xs mt-4 text-center">{lang.t('description')}</div>

      <TokenSelector
        isOpen={isOpenTokenSelector}
        tokens={tokenStore.tokenList}
        onOpenChange={onOpenTokenSelectorChange}
        onTokenSelect={handleTokenSelect}
      />

      {accountStore.currentNetwork && (
        <NetworkSwitcher
          fromNetwork={accountStore.currentNetwork}
          isOpen={isOpenNetworkSwitcher}
          toNetwork={depositStore.fromNetwork}
          onConfirm={() => {}}
          onOpenChange={onOpenNetworkSwitcherChange}
        />
      )}
    </div>
  )
})

export default Bridge
