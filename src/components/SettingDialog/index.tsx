import { useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>eader,
  ModalBody,
  Button,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  Switch,
  Image,
} from '@heroui/react'
import { useTheme } from '@heroui/use-theme'
import { useStore } from '@/store'
import { observer } from 'mobx-react-lite'

export interface SettingDialogProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
}

export const SettingDialog = observer(({ isOpen, onOpenChange }: SettingDialogProps) => {
  const { theme, setTheme } = useTheme()
  const { lang } = useStore()
  const [isAfqExpanded, setIsAfqExpanded] = useState(false)

  // 语言选项
  const languageOptions = [
    { key: 'en', label: 'English' },
    { key: 'zh_CN', label: '中文' },
  ]

  const currentLanguage = languageOptions.find(option => option.key === lang.lang) || languageOptions[0]

  const handleLanguageChange = async (keys: any) => {
    const selectedKey = Array.from(keys)[0] as string
    await lang.setLang(selectedKey)
  }

  const handleThemeToggle = () => {
    setTheme(theme === 'light' ? 'dark' : 'light')
  }

  return (
    <Modal
      hideCloseButton
      classNames={{
        base: 'bg-[#2D2B3A] border border-[#3A3847] rounded-2xl max-w-[400px]',
        header: 'border-b-0 pb-2',
        body: 'py-4',
      }}
      isOpen={isOpen}
      motionProps={{
        variants: {
          enter: {
            y: 0,
            opacity: 1,
            transition: {
              duration: 0.3,
              ease: 'easeOut',
            },
          },
          exit: {
            y: -10,
            opacity: 0,
            transition: {
              duration: 0.2,
              ease: 'easeIn',
            },
          },
        },
      }}
      size="sm"
      onOpenChange={onOpenChange}
    >
      <ModalContent>
        {onClose => (
          <>
            <ModalHeader className="flex justify-between items-center px-6 pt-6">
              <h2 className="text-xl font-medium text-white">Setting</h2>
              <Button
                isIconOnly
                className="text-white/70 hover:text-white hover:bg-white/10 min-w-6 h-6"
                variant="light"
                onPress={onClose}
              >
                <Image className="size-6" src="/public/images/icon_close.svg" />
              </Button>
            </ModalHeader>
            <ModalBody className="px-6 pb-6">
              <div className="space-y-6">
                {/* Language Setting */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Image className="size-6" src="/public/images/icon_language.svg" />
                    <span className="text-white text-base">Language</span>
                  </div>
                  <Dropdown>
                    <DropdownTrigger>
                      <Button
                        className="bg-[#3A3847] text-white border-none hover:bg-[#4A4857] min-w-[120px] justify-between"
                        endContent={<Image className="size-6" src="/public/images/icon_down.svg" />}
                        variant="flat"
                      >
                        {currentLanguage.label}
                      </Button>
                    </DropdownTrigger>
                    <DropdownMenu
                      aria-label="Language selection"
                      classNames={{
                        base: 'bg-[#2D2B3A] border border-[#3A3847]',
                        list: 'bg-[#2D2B3A]',
                      }}
                      selectedKeys={new Set([lang.lang])}
                      selectionMode="single"
                      onSelectionChange={handleLanguageChange}
                    >
                      {languageOptions.map(option => (
                        <DropdownItem
                          key={option.key}
                          className="text-white hover:bg-[#3A3847] data-[selected=true]:bg-[#8F7EFF]/20 data-[selected=true]:text-[#8F7EFF]"
                        >
                          {option.label}
                        </DropdownItem>
                      ))}
                    </DropdownMenu>
                  </Dropdown>
                </div>

                {/* Dark Mode Setting */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Image className="size-6" src="/public/images/icon_moon.svg" />
                    <span className="text-white text-base">Dark mode</span>
                  </div>
                  <Switch
                    classNames={{
                      base: 'inline-flex flex-row-reverse w-auto bg-transparent hover:bg-transparent',
                      wrapper: 'p-0 h-6 w-12 bg-[#3A3847] group-data-[selected=true]:bg-[#8F7EFF] rounded-full',
                      thumb:
                        'w-5 h-5 border-0 shadow-lg bg-white rounded-full group-data-[selected=true]:ml-6 ml-0.5 transition-all',
                    }}
                    isSelected={theme === 'dark'}
                    onValueChange={handleThemeToggle}
                  />
                </div>

                {/* AFQ Setting */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Image className="size-6" src="/public/images/icon_help.svg" />
                    <span className="text-white text-base">AFQ</span>
                  </div>
                  <Button
                    isIconOnly
                    className={`text-white/70 hover:text-white hover:bg-white/10 min-w-6 h-6 transition-transform ${
                      isAfqExpanded ? 'rotate-180' : ''
                    }`}
                    variant="light"
                    onPress={() => setIsAfqExpanded(!isAfqExpanded)}
                  >
                    <Image className="size-6" src="/public/images/icon_down.svg" />
                  </Button>
                </div>

                {/* AFQ Expanded Content */}
                {isAfqExpanded && (
                  <div className="ml-8 space-y-3 text-white/80 text-sm">
                    <div className="border-l-2 border-[#8F7EFF]/30 pl-4">
                      <p className="font-medium mb-1">How to bridge tokens?</p>
                      <p className="text-white/60">
                        Connect your wallet, select networks and tokens, then confirm the transaction.
                      </p>
                    </div>
                    <div className="border-l-2 border-[#8F7EFF]/30 pl-4">
                      <p className="font-medium mb-1">What are the fees?</p>
                      <p className="text-white/60">Bridge fees vary by network. Gas fees are paid separately.</p>
                    </div>
                    <div className="border-l-2 border-[#8F7EFF]/30 pl-4">
                      <p className="font-medium mb-1">How long does bridging take?</p>
                      <p className="text-white/60">Usually 5-30 minutes depending on network congestion.</p>
                    </div>
                  </div>
                )}
              </div>
            </ModalBody>
          </>
        )}
      </ModalContent>
    </Modal>
  )
})
